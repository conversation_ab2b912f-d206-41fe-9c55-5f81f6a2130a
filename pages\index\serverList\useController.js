import { getDevicesInfo } from '@/utils/common.js';
import { useConfigStore } from '@/store';
import { getServerList as getServerListApi } from '@/api/serverList';
import { PATH, UUID, SECRET, AESKEY, SYSTEM } from '@/utils/config';
import { useServerListStore } from './store';
import { hasKey, getPercentage, getByteUnit } from '@/utils';
import Message from '@/hooks/useMessage';
import { ref, nextTick } from 'vue';
import { triggerVibrate } from '@/utils/common';
import { handleServerInfo as handleServerInfoData } from '@/linux/index/useController';
import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import { $t } from '@/locale/index.js';
import {
	extractIPFromURL,
	generateSafeCanvasId,
	formatIPDisplay as formatIPDisplayUtil,
	compareIPs,
	normalizeIP,
	normalizeURL,
} from '@/utils/ipUtils';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

export const paging = ref(null);
export const pageContainer = ref(null);
export const iosPromptModel = ref(false);
export const harmonyPromptModel = ref(false);
export const harmonyInfo = ref({});
export const harmonyDeviceInfo = ref({});

export const getDevicesInfoEvent = () => {
	const { phoneBrand, phoneModel } = useConfigStore().getReactiveState();
	getDevicesInfo().then((res) => {
		phoneBrand.value = res.brand;
		phoneModel.value = res.model;
	});
};

export const confirmIosPrompt = (close) => {
	uni.navigateTo({
		url: '/pages/novice/novice?type=ios',
		animationType: 'zoom-fade-out',
		success: () => {
			close && close();
		},
	});
};

export const handleCloudBindSuccess = (cloudCode) => {
	const { serverInit, serverList } = useServerListStore().getReactiveState();
	serverList.value = serverInit.value;
};

// 添加一个标志来防止在手动添加服务器时被下拉刷新干扰
let isManuallyAddingServer = false;

export const handleBindSuccess = async (bindInfo) => {
	try {
		// 设置标志，防止下拉刷新干扰
		isManuallyAddingServer = true;

		const { serverList } = useServerListStore().getReactiveState();

		// 【新增】立即创建并插入加载状态的服务器项
		const loadingServerItem = createLoadingServerItem(bindInfo);

		// 【新增】去重检查 - 移除可能存在的重复项，基于SECRET值进行唯一性判断
		const duplicateChecks = [
			(server) => server.uniqueKey === loadingServerItem.uniqueKey,
			(server) => server.rawPath === loadingServerItem.rawPath,
		];

		// 查找并移除重复项
		const duplicateIndices = [];
		serverList.value.forEach((server, index) => {
			const isDuplicate = duplicateChecks.some((check) => check(server));
			if (isDuplicate) {
				duplicateIndices.push(index);
			}
		});

		// 从后往前删除重复项
		for (let i = duplicateIndices.length - 1; i >= 0; i--) {
			serverList.value.splice(duplicateIndices[i], 1);
		}

		// 同时清理虚拟列表中的重复项
		const virtualDuplicateIndices = [];
		virtualListData.value.forEach((server, index) => {
			const isDuplicate = duplicateChecks.some((check) => check(server));
			if (isDuplicate) {
				virtualDuplicateIndices.push(index);
			}
		});

		for (let i = virtualDuplicateIndices.length - 1; i >= 0; i--) {
			virtualListData.value.splice(virtualDuplicateIndices[i], 1);
		}

		// 【新增】立即添加加载状态的服务器项到列表首部
		serverList.value.unshift(loadingServerItem);

		// 【修复】立即更新排序列表 - 确保新服务器始终在首部
		const savedSortOrder = uni.getStorageSync('serverSortOrder');
		if (savedSortOrder && Array.isArray(savedSortOrder)) {
			// 先彻底移除可能存在的重复项
			const cleanedSortOrder = savedSortOrder.filter((rawPath) => rawPath !== loadingServerItem.rawPath);
			// 将新服务器添加到首部
			cleanedSortOrder.unshift(loadingServerItem.rawPath);
			uni.setStorageSync('serverSortOrder', cleanedSortOrder);
		} else {
			// 如果没有排序列表，创建一个新的，新服务器在首部
			uni.setStorageSync('serverSortOrder', [loadingServerItem.rawPath]);
		}

		// 【新增】立即更新UI显示
		if (paging.value) {
			paging.value.setLocalPaging(serverList.value);
			paging.value.updateVirtualListRender();
		}

		// 【新增】异步处理API请求，更新服务器状态
		handleServerItemAsync(bindInfo, loadingServerItem.uniqueKey);
	} catch (error) {
		// 如果处理失败，则刷新整个列表
		setTimeout(() => {
			if (paging.value) {
				paging.value.reload();
			}
		}, 500);
	} finally {
		// 重置标志
		setTimeout(() => {
			isManuallyAddingServer = false;
		}, 1000);
	}
};

// 导出标志供其他地方使用
export const getIsManuallyAddingServer = () => isManuallyAddingServer;

// 新增：存储虚拟列表当前显示的数据
export const virtualListData = ref([]);

// 创建服务器基础信息对象
const createServerBasicInfo = (config, index = 0) => {
	const { serverInit } = useServerListStore().getReactiveState();

	// 使用深拷贝避免修改原始数据
	let basicInfo = JSON.parse(JSON.stringify(serverInit.value[0]));

	// 设置基础信息
	basicInfo.name = config.name || $t('server.connecting');
	basicInfo.ip = extractIPFromURL(config.panelPath);
	basicInfo.rawPath = config.panelPath;
	basicInfo.status = null; // null表示状态未知，正在加载
	basicInfo.isLoadingStatus = true; // 添加加载状态标识

	// 添加稳定的唯一标识符 - 使用SECRET值（panelKey）作为唯一标识符
	// SECRET值有效性验证，如果panelKey不存在或为空，回退到panelPath
	const secretKey = config.panelKey && config.panelKey.trim() !== '' ? config.panelKey : config.panelPath;
	basicInfo.uniqueKey = secretKey;
	basicInfo.uniqueId = `${secretKey}_${Date.now()}_${index}`;

	return basicInfo;
};

// 跟踪已完成加载的服务器
const loadedServers = new Set();

// 首屏加载状态跟踪
let isFirstScreenLoad = true;
const firstScreenLoadingServers = new Set();

// 重置首屏加载状态（用于页面刷新或重新进入）
export const resetFirstScreenLoadState = () => {
	isFirstScreenLoad = true;
	firstScreenLoadingServers.clear();
};

// 创建加载状态的服务器项
export const createLoadingServerItem = (bindInfo) => {
	const { serverInit } = useServerListStore().getReactiveState();

	// 使用深拷贝避免修改原始数据
	let basicInfo = JSON.parse(JSON.stringify(serverInit.value[0]));

	// 设置基础信息
	basicInfo.name = bindInfo.name || $t('server.connecting');
	basicInfo.ip = extractIPFromURL(bindInfo.panelPath);
	basicInfo.rawPath = bindInfo.panelPath;
	basicInfo.status = null; // null表示状态未知，正在加载
	basicInfo.isLoadingStatus = true; // 添加加载状态标识
	basicInfo.isManuallyAdding = true; // 标记为手动添加的服务器

	// 添加稳定的唯一标识符 - 使用SECRET值（panelKey）作为唯一标识符
	// SECRET值有效性验证，如果panelKey不存在或为空，回退到panelPath
	const secretKey = bindInfo.panelKey && bindInfo.panelKey.trim() !== '' ? bindInfo.panelKey : bindInfo.panelPath;
	basicInfo.uniqueKey = secretKey;
	basicInfo.uniqueId = `${secretKey}_${Date.now()}_loading`;

	// 重置一些可能影响显示的字段
	basicInfo.uptime = $t('server.connecting');
	basicInfo.network = { up: '--', down: '--' };
	basicInfo.cpu = { used: 0, total: 100 };
	basicInfo.memory = { used: 0, total: 0 };
	basicInfo.disk = { used: 0, total: 0 };

	return basicInfo;
};

// 异步处理服务器API请求，更新已存在的加载状态服务器项
export const handleServerItemAsync = async (bindInfo, uniqueKey) => {
	try {
		// 获取完整的服务器数据
		const fullServerData = await handleServerItem(bindInfo);

		if (fullServerData) {
			const { serverList } = useServerListStore().getReactiveState();

			// 查找并更新对应的服务器项
			const serverIndex = serverList.value.findIndex((server) => server.uniqueKey === uniqueKey);

			if (serverIndex !== -1) {
				// 保持原有的标识符和位置
				fullServerData.uniqueKey = uniqueKey;
				fullServerData.isLoadingStatus = false;
				fullServerData.isManuallyAdding = false;

				// 更新服务器列表中的项
				serverList.value[serverIndex] = fullServerData;

				// 同时更新虚拟列表中的对应项
				updateServerInLists(fullServerData, uniqueKey);

				// 标记服务器为已加载
				loadedServers.add(uniqueKey);

				// 更新UI显示
				if (paging.value) {
					paging.value.setLocalPaging(serverList.value);
					setTimeout(() => {
						paging.value.updateVirtualListRender && paging.value.updateVirtualListRender();
					}, 100);
				}
			}
		}
	} catch (error) {
		// 处理失败情况 - 移除加载失败的服务器项或标记为失败状态
		handleServerLoadFailure(uniqueKey, error);
	}
};

// 处理服务器加载失败的情况
const handleServerLoadFailure = (uniqueKey, error) => {
	const { serverList } = useServerListStore().getReactiveState();

	const serverIndex = serverList.value.findIndex((server) => server.uniqueKey === uniqueKey);

	if (serverIndex !== -1) {
		// 选择1: 移除失败的服务器项
		// serverList.value.splice(serverIndex, 1);

		// 选择2: 标记为失败状态（推荐，用户可以看到失败原因）
		const failedServer = {
			...serverList.value[serverIndex],
			name: $t('common.fail'),
			status: false,
			isLoadingStatus: false,
			isManuallyAdding: false,
			errorMessage: error.message || '连接失败',
		};

		serverList.value[serverIndex] = failedServer;

		// 更新虚拟列表
		updateServerInLists(failedServer, uniqueKey);

		// 更新UI显示
		if (paging.value) {
			paging.value.setLocalPaging(serverList.value);
			setTimeout(() => {
				paging.value.updateVirtualListRender && paging.value.updateVirtualListRender();
			}, 100);
		}
	}
};

// 快速获取服务器基础信息列表
export const getServerBasicList = () => {
	const { configList } = useConfigStore().getReactiveState();
	const { serverList } = useServerListStore().getReactiveState();

	// 从配置列表创建基础信息列表
	const basicList = configList.value.map((config, index) => {
		// 检查是否已存在该服务器的数据 - 基于SECRET值（panelKey）进行匹配
		// SECRET值有效性验证，如果panelKey不存在或为空，回退到panelPath
		const configSecretKey = config.panelKey && config.panelKey.trim() !== '' ? config.panelKey : config.panelPath;
		const existingServer = serverList.value.find(
			(server) => server.rawPath === config.panelPath || server.uniqueKey === configSecretKey,
		);

		if (existingServer) {
			// 检查服务器是否已经完成加载
			const serverKey = existingServer.uniqueKey || configSecretKey;
			const hasLoadedBefore = loadedServers.has(serverKey);

			// 更严格的加载状态判断：只有在真正需要重新加载时才标记为loading
			// 已成功加载的服务器（status=true且有有效名称）永远不应该重新进入loading状态
			const isValidLoadedServer =
				existingServer.status === true &&
				existingServer.name &&
				existingServer.name !== $t('server.connecting') &&
				existingServer.name !== $t('common.fail');

			// 只有在以下情况下才标记为需要加载：
			// 1. 服务器状态为null（完全未知）
			// 2. 名称明确为连接中状态
			// 3. 名称明确为失败状态
			// 4. 从未尝试过加载（新服务器）
			const needsLoading =
				!isValidLoadedServer &&
				!hasLoadedBefore &&
				(existingServer.status === null ||
					existingServer.name === $t('server.connecting') ||
					existingServer.name === $t('common.fail'));

			return {
				...existingServer,
				isLoadingStatus: needsLoading,
				uniqueKey: serverKey,
			};
		} else {
			// 创建新的基础信息
			return createServerBasicInfo(config, index);
		}
	});

	// 【修复】应用排序顺序 - 确保新服务器始终在首部
	const savedSortOrder = uni.getStorageSync('serverSortOrder');
	if (savedSortOrder && Array.isArray(savedSortOrder) && savedSortOrder.length > 0) {
		const orderedList = [];
		const remainingServers = [...basicList];

		// 按照保存的排序顺序添加服务器
		savedSortOrder.forEach((rawPath) => {
			const index = remainingServers.findIndex((server) => server.rawPath === rawPath);
			if (index !== -1) {
				orderedList.push(remainingServers.splice(index, 1)[0]);
			}
		});

		// 【修复】将不在排序列表中的服务器添加到首部（这些通常是新添加的服务器）
		// 这样确保新服务器始终出现在列表顶部
		const finalList = [...remainingServers, ...orderedList];
		return finalList;
	}

	return basicList;
};

export const getServerList = async () => {
	try {
		const { serverList } = useServerListStore().getReactiveState();

		// 如果正在手动添加服务器，跳过刷新以避免干扰
		if (getIsManuallyAddingServer()) {
			return serverList.value;
		}

		// 第一阶段：立即返回基础信息列表
		const basicList = getServerBasicList();
		serverList.value = basicList;

		// 第二阶段：异步加载状态数据
		loadServerStatusData(basicList);

		// 立即返回基础信息，让UI能够快速渲染
		return basicList;
	} catch (error) {
		return [];
	}
};

// 异步加载服务器状态数据 - 并行版本
const loadServerStatusData = async (basicList) => {
	const { configList } = useConfigStore().getReactiveState();

	// 如果是首屏加载，记录需要加载的服务器
	const serversToLoad = basicList.filter((server) => server.isLoadingStatus);
	if (isFirstScreenLoad && serversToLoad.length > 0) {
		serversToLoad.forEach((server) => {
			firstScreenLoadingServers.add(server.uniqueKey);
		});
	}

	// 【重构】移除批次处理，改为真正的并行请求
	// 为每个服务器创建独立的异步处理函数
	const processServerAsync = async (basicServer) => {
		// 查找对应的配置项
		const configItem = configList.value.find((config) => config.panelPath === basicServer.rawPath);
		if (!configItem) return null;

		try {
			// 获取完整的服务器数据
			const fullServerData = await handleServerItem(configItem);
			if (fullServerData) {
				// 保持原有的标识符
				fullServerData.uniqueKey = basicServer.uniqueKey;
				fullServerData.uniqueId = basicServer.uniqueId;
				if (basicServer.zp_index !== undefined) {
					fullServerData.zp_index = basicServer.zp_index;
				}

				// 【优化】首屏加载时延迟隐藏加载状态，确保图表有足够时间渲染
				if (isFirstScreenLoad && firstScreenLoadingServers.has(basicServer.uniqueKey)) {
					// 首屏加载：先更新数据但保持加载状态，延迟隐藏加载状态
					fullServerData.isLoadingStatus = true;
					updateServerInLists(fullServerData, basicServer.uniqueKey);

					// 延迟隐藏加载状态，给图表渲染留出时间
					setTimeout(() => {
						fullServerData.isLoadingStatus = false;
						updateServerInLists(fullServerData, basicServer.uniqueKey);
						// 从首屏加载集合中移除
						firstScreenLoadingServers.delete(basicServer.uniqueKey);

						// 如果所有首屏服务器都完成了，标记首屏加载结束
						if (firstScreenLoadingServers.size === 0) {
							isFirstScreenLoad = false;
						}
					}, 1000); // 延迟1000ms，确保图表动画完成
				} else {
					// 非首屏加载：立即隐藏加载状态
					fullServerData.isLoadingStatus = false;
					updateServerInLists(fullServerData, basicServer.uniqueKey);
				}

				// 标记服务器为已加载
				loadedServers.add(basicServer.uniqueKey);
			}
			return fullServerData;
		} catch (error) {
			// 加载失败时的处理
			const failedServerData = {
				...basicServer,
				name: $t('common.fail'),
				status: false,
				isLoadingStatus: false,
			};

			// 如果是首屏加载，也要从集合中移除
			if (isFirstScreenLoad && firstScreenLoadingServers.has(basicServer.uniqueKey)) {
				firstScreenLoadingServers.delete(basicServer.uniqueKey);
				if (firstScreenLoadingServers.size === 0) {
					isFirstScreenLoad = false;
				}
			}

			// 【关键】失败的服务器也立即更新UI
			updateServerInLists(failedServerData, basicServer.uniqueKey);
			return null;
		}
	};

	// 【重构】所有服务器请求同时发起，真正的并行处理
	const allServerPromises = basicList.map(processServerAsync);

	// 【重构】使用 Promise.allSettled 而不是 Promise.all
	// 这样即使某些服务器失败，也不会影响其他服务器的处理
	// 注意：我们不等待所有Promise完成，因为每个Promise完成时已经更新了UI
	Promise.allSettled(allServerPromises).then(() => {
		// 所有服务器处理完成后，应用最终排序
		applyFinalSorting();
	});

	// 【重构】立即返回，不等待所有服务器加载完成
	// 这样UI可以立即显示基础信息，然后逐步更新每个服务器的详细数据
};

// 更新服务器在各个列表中的数据 - 并发安全版本
const updateServerInLists = (serverData, uniqueKey) => {
	try {
		const { serverList } = useServerListStore().getReactiveState();

		// 【并发安全】验证输入参数
		if (!serverData || !uniqueKey) {
			return;
		}

		// 【并发安全】更新主服务器列表
		const serverIndex = serverList.value.findIndex((server) => server.uniqueKey === uniqueKey);
		if (serverIndex !== -1) {
			// 保持原有的zp_index等虚拟列表相关属性
			const originalServer = serverList.value[serverIndex];

			// 【并发安全】确保原始服务器仍然存在（避免并发删除导致的问题）
			if (originalServer) {
				const updatedServer = {
					...serverData,
					zp_index: originalServer.zp_index, // 保持虚拟列表索引
					// 【新增】保持一些关键的原始属性，避免并发更新时丢失
					uniqueKey: originalServer.uniqueKey,
					uniqueId: originalServer.uniqueId,
				};

				// 【并发安全】使用Vue的响应式更新
				serverList.value.splice(serverIndex, 1, updatedServer);
			}
		}

		// 【并发安全】更新虚拟列表数据
		const virtualIndex = virtualListData.value.findIndex((server) => server.uniqueKey === uniqueKey);
		if (virtualIndex !== -1) {
			// 保持原有的虚拟列表属性，确保数据完整性
			const originalVirtualServer = virtualListData.value[virtualIndex];

			// 【并发安全】确保原始虚拟服务器仍然存在
			if (originalVirtualServer) {
				// 深度合并数据，确保所有字段都得到正确更新
				const updatedVirtualServer = {
					...serverData,
					zp_index: originalVirtualServer.zp_index, // 保持虚拟列表索引
					uniqueKey: originalVirtualServer.uniqueKey, // 保持唯一标识
					uniqueId: originalVirtualServer.uniqueId, // 保持唯一ID
					// 确保关键的显示数据得到更新，但保留原有数据作为回退
					cpu: serverData.cpu || originalVirtualServer.cpu,
					memory: serverData.memory || originalVirtualServer.memory,
					disk: serverData.disk || originalVirtualServer.disk,
					network: serverData.network || originalVirtualServer.network,
					load: serverData.load || originalVirtualServer.load,
				};

				// 【并发安全】使用Vue的响应式更新，避免直接赋值可能导致的渲染问题
				virtualListData.value.splice(virtualIndex, 1, updatedVirtualServer);
			}
		}

		// 【新增】触发UI更新，确保并行加载的数据能够及时显示
		nextTick(() => {
			if (paging.value && paging.value.updateVirtualListRender) {
				paging.value.updateVirtualListRender();
			}
		});
	} catch (error) {
		// 【并发安全】静默处理更新错误，避免单个服务器的更新失败影响整体
		console.warn('更新服务器列表时发生错误:', error);
	}
};

// 【新增】删除操作后同步虚拟列表数据
const syncVirtualListAfterDelete = (deletedItem) => {
	// 【修复】使用更严格的匹配逻辑，避免误删
	const indicesToRemove = [];

	virtualListData.value.forEach((server, index) => {
		// 使用多种方式匹配，确保能正确找到要删除的项
		const isMatch =
			server.uniqueKey === deletedItem.uniqueKey ||
			server.ip === deletedItem.ip ||
			server.rawPath === deletedItem.rawPath ||
			compareIPs(server.ip, deletedItem.ip);

		if (isMatch) {
			indicesToRemove.push(index);
		}
	});

	// 【修复】从后往前删除，避免索引偏移问题
	let removedCount = 0;
	for (let i = indicesToRemove.length - 1; i >= 0; i--) {
		const index = indicesToRemove[i];
		virtualListData.value.splice(index, 1);
		removedCount++;
	}

	return removedCount > 0;
};

// 【修复】应用最终排序 - 确保新服务器始终在首部
const applyFinalSorting = () => {
	const { serverList } = useServerListStore().getReactiveState();
	const savedSortOrder = uni.getStorageSync('serverSortOrder');

	if (savedSortOrder && Array.isArray(savedSortOrder) && savedSortOrder.length > 0) {
		const orderedList = [];
		const remainingServers = [...serverList.value];

		// 按照保存的排序顺序添加服务器
		savedSortOrder.forEach((rawPath) => {
			const index = remainingServers.findIndex((server) => server.rawPath === rawPath);
			if (index !== -1) {
				orderedList.push(remainingServers.splice(index, 1)[0]);
			}
		});

		// 【修复】将不在排序列表中的服务器添加到首部（这些通常是新添加的服务器）
		const finalList = [...remainingServers, ...orderedList];
		serverList.value = finalList;
	}
};

export const handleServerItem = async (item) => {
	const { serverInit } = useServerListStore().getReactiveState();
	const { panelPath, panelKey, aesKey, uuid, system } = item;

	// 【修复】不再设置全局变量，而是将配置作为参数传递给API请求
	// 这样可以避免异步请求时的全局变量竞态条件问题
	const serverConfig = {
		panelPath,
		panelKey,
		aesKey,
		uuid,
		system,
	};

	// 准备服务器对象 - 使用深拷贝避免修改原始数据
	let initJson = JSON.parse(JSON.stringify(serverInit.value)),
		initPath = panelPath;
	initJson[0].name = $t('server.connecting');
	// 使用新的IP提取函数，支持IPv4和IPv6
	initJson[0].ip = extractIPFromURL(initPath);
	initJson[0].rawPath = initPath;
	initJson[0].status = true;

	let temp = JSON.stringify(initJson);
	temp = temp.replace(/\[|]/g, '');
	let serverItem = JSON.parse(temp);

	// 为服务器项添加稳定的唯一标识符，用于Vue的key绑定
	serverItem.uniqueKey = `${serverItem.ip}_${initPath}`;

	// 【修复】创建一个Promise用于处理API请求和超时，增加更详细的错误处理
	return new Promise((resolve, reject) => {
		// 【修复】增加超时时间到15秒，给网络较慢的服务器更多时间
		// 考虑到网络波动、服务器负载等因素，15秒是一个更合理的超时时间
		let setTime = setTimeout(() => {
			serverItem.name = $t('common.fail');
			serverItem.status = false;
			// 【修复】超时时使用reject而不是resolve，让调用方能够区分超时和正常失败
			reject(new Error('请求超时 (15秒)'));
		}, 15000);

		// 发起API请求，传递服务器配置
		getServerListApi(serverConfig)
			.then((res) => {
				clearTimeout(setTime);
				// 处理API响应
				if (hasKey(res, 'data')) {
					if (res.data && res.data.msg) {
						// 轮询时不显示错误消息，避免频繁弹窗
					}
					serverItem.status = false;
					serverItem.name = $t('common.fail');
				} else {
					// 成功获取服务器信息，更新服务器对象
					serverItem.status = true;
					serverItem.name = res.title;

					let tempTime = res.time;
					if (serverItem.system == 'windows') {
						tempTime = tempTime.substr(0, tempTime.indexOf('天') + 1);
					}
					serverItem.uptime = tempTime;
					serverItem.network.up = res.up;
					serverItem.network.down = res.down;

					// 处理CPU数据
					serverItem.cpu.usage = res.cpu[0]?.toFixed(1);
					serverItem.cpu.cores = res.cpu[1];

					// 处理负载数据
					let loadCount =
						Math.round((res.load.one / res.load.max) * 100) > 100
							? 100
							: Math.round((res.load.one / res.load.max) * 100);
					loadCount = loadCount < 0 ? 0 : loadCount;
					const loadInfo = handleServerInfoData(loadCount, 'load');
					serverItem.load.usage = loadInfo.val?.toFixed(1);
					serverItem.load.total = loadInfo.title;

					// 处理内存数据
					serverItem.memory.usage = getPercentage(res.mem.memTotal, res.mem.memRealUsed)?.toFixed(1);
					if (DEFAULT_API_TYPE === 'domestic') {
						serverItem.memory.total = res.mem.memNewTotal;
					} else {
						serverItem.memory.total = `${res.mem.memTotal} MB`;
					}

					// 磁盘
					let zeroRatio = res.disk[0].size[3];
					zeroRatio = parseFloat(zeroRatio.substring(0, zeroRatio.lastIndexOf('%')));
					serverItem.disk.usage = zeroRatio?.toFixed(1);
					serverItem.disk.total = res.disk[0].size[0];
				}

				// 注释掉直接更新 serverList 的逻辑，避免重复添加
				// 数据更新将由调用方统一处理
				resolve(serverItem);
			})
			.catch((error) => {
				clearTimeout(setTime);
				serverItem.name = $t('common.fail');
				serverItem.status = false;

				// 传递具体的错误信息，便于调用方进行错误分析
				reject(error);
			});
	});
};

// 跟踪正在进行的轮询请求，防止重复请求
const pollingRequests = new Map();

// 轮询失败计数器，用于错误处理
const pollingFailureCount = new Map();

// 【简化】移除复杂的重试机制，保持简洁的轮询逻辑

export const getServerInfoList = async () => {
	const { configList } = useConfigStore().getReactiveState();

	// 只轮询当前虚拟列表中显示的服务器项
	if (!virtualListData.value || virtualListData.value.length === 0) {
		return; // 如果虚拟列表为空，不进行轮询
	}

	// 过滤出需要更新的服务器（非加载状态且在线的服务器）
	const serversToUpdate = virtualListData.value.filter((server) => {
		// 【修复】增加请求去重检查，避免重复请求同一服务器
		const isRequestInProgress = pollingRequests.has(server.uniqueKey);
		return !server.isLoadingStatus && server.status === true && !isRequestInProgress;
	});

	if (serversToUpdate.length === 0) {
		return; // 没有需要更新的服务器
	}

	// 分批更新，避免同时发起过多请求
	const batchSize = 2; // 轮询时使用更小的批次
	const batches = [];
	for (let i = 0; i < serversToUpdate.length; i += batchSize) {
		batches.push(serversToUpdate.slice(i, i + batchSize));
	}

	// 逐批处理服务器更新
	for (const batch of batches) {
		const batchPromises = batch.map(async (virtualServer) => {
			// 【修复】标记请求开始，防止重复请求
			pollingRequests.set(virtualServer.uniqueKey, Date.now());

			try {
				// 查找对应的配置项
				let configItem = configList.value.find((config) => config.panelPath === virtualServer.rawPath);

				// 方式2: 如果直接匹配失败，尝试通过SECRET值（panelKey）匹配
				if (!configItem) {
					configItem = configList.value.find((config) => {
						// SECRET值有效性验证，如果panelKey不存在或为空，回退到panelPath
						const configSecretKey =
							config.panelKey && config.panelKey.trim() !== '' ? config.panelKey : config.panelPath;
						return virtualServer.uniqueKey === configSecretKey;
					});
				}

				// 方式3: 如果还是失败，尝试标准化路径匹配
				if (!configItem) {
					const normalizedServerPath = normalizeURL(virtualServer.rawPath);
					configItem = configList.value.find((config) => {
						const normalizedConfigPath = normalizeURL(config.panelPath);
						return normalizedServerPath === normalizedConfigPath;
					});
				}

				if (configItem) {
					try {
						const updatedServerData = await handleServerItem(configItem);
						if (updatedServerData) {
							// 保持原有的标识符
							updatedServerData.uniqueKey = virtualServer.uniqueKey;
							updatedServerData.uniqueId = virtualServer.uniqueId;
							if (virtualServer.zp_index !== undefined) {
								updatedServerData.zp_index = virtualServer.zp_index;
							}
							updatedServerData.isLoadingStatus = false;

							// 使用统一的更新函数
							updateServerInLists(updatedServerData, virtualServer.uniqueKey);

							// 【修复】重置失败计数器（请求成功）
							pollingFailureCount.delete(virtualServer.uniqueKey);

							// 【优化】返回成功结果，用于 Promise.allSettled 统计
							return { success: true, server: virtualServer.uniqueKey, status: 'updated' };
						} else {
							// 【优化】API返回空数据时的处理
							return { success: false, server: virtualServer.uniqueKey, status: 'empty_response' };
						}
					} catch (serverError) {
						// 【修复】handleServerItem 现在可能会 reject，需要在这里处理
						throw serverError;
					}
				} else {
					// 【优化】配置项未找到时的处理
					return { success: false, server: virtualServer.uniqueKey, status: 'config_not_found' };
				}
			} catch (error) {
				// 【修复】改进错误处理，增加容错机制，避免网络波动导致的误判
				const currentFailures = pollingFailureCount.get(virtualServer.uniqueKey) || 0;
				const newFailureCount = currentFailures + 1;
				pollingFailureCount.set(virtualServer.uniqueKey, newFailureCount);

				// 【修复】提高失败阈值：连续失败3次后才将服务器标记为离线状态
				// 这样可以避免网络抖动、临时超时等情况导致的误判
				const FAILURE_THRESHOLD = 2;
				if (newFailureCount >= FAILURE_THRESHOLD) {
					const offlineServerData = {
						...virtualServer,
						status: false,
						name: virtualServer.name, // 保持原名称，不改为"连接失败"
						isLoadingStatus: false,
					};

					updateServerInLists(offlineServerData, virtualServer.uniqueKey);

					// 【新增】记录离线原因，便于调试
					console.warn(`服务器 ${virtualServer.name} 连续失败 ${newFailureCount} 次，标记为离线`, {
						uniqueKey: virtualServer.uniqueKey,
						error: error.message,
						timestamp: new Date().toISOString(),
					});
				} else {
					// 【新增】失败次数未达到阈值时，记录警告但不改变状态
					console.warn(`服务器 ${virtualServer.name} 请求失败 ${newFailureCount}/${FAILURE_THRESHOLD} 次`, {
						uniqueKey: virtualServer.uniqueKey,
						error: error.message,
					});
				}

				// 【简化】移除复杂的重试逻辑，保持简洁的错误处理

				// 【优化】返回失败结果，用于 Promise.allSettled 统计
				return {
					success: false,
					server: virtualServer.uniqueKey,
					status: 'error',
					error: error.message,
					failureCount: newFailureCount,
				};
			} finally {
				// 清除请求标记
				pollingRequests.delete(virtualServer.uniqueKey);
			}
		});

		// 【关键修复】使用 Promise.allSettled 替代 Promise.all
		// 这样即使某些服务器失败，也不会影响其他服务器的处理
		await Promise.allSettled(batchPromises);

		// 批次间稍作延迟
		if (batches.indexOf(batch) < batches.length - 1) {
			await new Promise((resolve) => setTimeout(resolve, 100)); // 增加延迟到100ms
		}
	}
};

// 图表配置函数
export const getChartColorStops = (usage) => {
	if (usage >= 81) {
		// 81-100% 显示纯红色
		return [
			{ offset: 0, color: '#D32F2F' }, // 橙色
			{ offset: 1, color: '#FFEB3B' }, // 红色
		];
	} else if (usage >= 51) {
		// 51-80% 从绿色到黄色的渐变
		return [
			{ offset: 0, color: '#4CAF50' }, // 绿色起点
			{ offset: 1, color: '#FFEB3B' }, // 黄色终点
		];
	} else {
		// 0-50% 纯绿色
		return [
			{ offset: 0, color: '#4CAF50' }, // 绿色
			{ offset: 1, color: '#4CAF50' }, // 绿色
		];
	}
};

// 获取基础图表配置
export const getBaseChartConfig = (usage, label, options = {}) => {
	// 确保usage是数字
	usage = parseFloat(usage) || 0;

	// 创建颜色渐变配置
	const colorStops = getChartColorStops(usage);

	// 创建仪表盘数据
	const gaugeData = [
		{
			value: usage,
			name: label,
			title: {
				show: false,
			},
			detail: {
				valueAnimation: true,
				offsetCenter: [0, '0%'],
				fontSize: 10,
				color: colorStops[0].color,
				formatter: '{value}%',
			},
			itemStyle: {
				color: {
					type: 'linear',
					x: 0,
					y: 0,
					x2: 1,
					y2: 0,
					colorStops: [...colorStops], // 使用深拷贝
				},
			},
		},
	];

	// 默认的 series 配置
	const defaultSeriesConfig = {
		type: 'gauge',
		radius: '100%',
		center: ['55%', '65%'],
		startAngle: 180,
		endAngle: 0,
		pointer: {
			show: false,
		},
		progress: {
			show: usage > 0, // 当数据为0时隐藏进度条
			overlap: false,
			roundCap: usage > 0, // 当数据为0时不使用圆形端点
			clip: false,
			itemStyle: {
				borderWidth: usage > 0 ? 1 : 0, // 当数据为0时移除边框
			},
		},
		axisLine: {
			lineStyle: {
				width: 4,
			},
		},
		axisTick: {
			show: false,
		},
		splitLine: {
			show: false,
		},
		axisLabel: {
			show: false,
			fontSize: 9,
		},
		title: {
			show: false,
		},
		data: gaugeData,
		detail: {
			valueAnimation: true,
			fontSize: 10,
			color: '#A7A7A7',
			formatter: '{value}%',
			borderRadius: 20,
		},
		// 优化动画配置，与首屏加载延迟时间匹配
		animation: true,
		animationDuration: 800, // 与延迟隐藏时间匹配
		animationDurationUpdate: 600,
		animationEasing: 'cubicInOut',
		animationDelay: 0,
	};

	// 使用 Object.assign 进行配置合并
	const mergedSeriesConfig = Object.assign({}, defaultSeriesConfig, options, {
		// 对于嵌套对象使用 Object.assign 进行深度合并
		progress: Object.assign({}, defaultSeriesConfig.progress, options.progress),
		axisLine: Object.assign({}, defaultSeriesConfig.axisLine, options.axisLine),
		axisLabel: Object.assign({}, defaultSeriesConfig.axisLabel, options.axisLabel),
		detail: Object.assign({}, defaultSeriesConfig.detail, options.detail),
	});

	// 返回图表配置
	return {
		series: [mergedSeriesConfig],
	};
};

// CPU图表配置 - 增强数据处理和边界情况处理
export const getCpuChartData = (cpuData, isOffline = false) => {
	// 数据安全性检查和默认值处理
	if (!cpuData || typeof cpuData !== 'object') {
		console.warn('getCpuChartData: 无效的CPU数据，使用默认值');
		return getBaseChartConfig(0, $t('server.cpu'));
	}

	// 确保usage字段存在且为有效数值
	const usage = typeof cpuData.usage === 'number' ? cpuData.usage : parseFloat(cpuData.usage) || 0;

	const options = {
		axisLine: {
			lineStyle: {
				width: 8,
				color: [[1, '#E7E7E7']],
			},
		},
		itemStyle: {
			color: '#20a50a',
		},
	};

	return getBaseChartConfig(usage, $t('server.cpu'), options);
};

// 内存图表配置 - 增强数据处理和边界情况处理
export const getMemChartData = (memData, serverStatus = true) => {
	// 数据安全性检查和默认值处理
	if (!memData || typeof memData !== 'object') {
		console.warn('getMemChartData: 无效的内存数据，使用默认值');
		return getBaseChartConfig(0, $t('server.memory'));
	}

	// 确保usage字段存在且为有效数值
	const usage = typeof memData.usage === 'number' ? memData.usage : parseFloat(memData.usage) || 0;

	const options = {
		axisLine: {
			lineStyle: {
				width: 8,
				color: [[1, '#E7E7E7']],
			},
		},
		itemStyle: {
			color: '#20a50a',
		},
	};

	return getBaseChartConfig(usage, $t('server.memory'), options);
};

// 磁盘图表配置 - 增强数据处理和边界情况处理
export const getDiskChartData = (diskData) => {
	// 数据安全性检查和默认值处理
	if (!diskData || typeof diskData !== 'object') {
		console.warn('getDiskChartData: 无效的磁盘数据，使用默认值');
		return getBaseChartConfig(0, $t('server.disk'));
	}

	// 确保usage字段存在且为有效数值
	const usage = typeof diskData.usage === 'number' ? diskData.usage : parseFloat(diskData.usage) || 0;

	return getBaseChartConfig(usage, $t('server.disk'));
};

// 服务器列表轮询
export const useServerListPolling = () => {
	let pollingTimer = null;
	let isPollingActive = false;

	// 【修复】改进的轮询函数，增加状态检查和错误恢复
	const executePolling = async () => {
		// 防止重复执行
		if (isPollingActive) {
			return;
		}

		isPollingActive = true;
		try {
			await getServerInfoList();
		} catch (error) {
			// 轮询执行失败，静默处理
		} finally {
			isPollingActive = false;
		}
	};

	// 开始轮询
	const startPolling = () => {
		// 先清除可能存在的定时器
		stopPolling();

		// 立即执行一次
		executePolling();

		// 【修复】增加轮询间隔到6秒，给服务器更多响应时间
		pollingTimer = setInterval(() => {
			executePolling();
		}, 6000);
	};

	// 停止轮询
	const stopPolling = () => {
		if (pollingTimer) {
			clearInterval(pollingTimer);
			pollingTimer = null;
		}
		isPollingActive = false;

		// 【修复】停止轮询时清理请求状态
		pollingRequests.clear();
	};

	// 【新增】重置轮询状态，用于错误恢复
	const resetPolling = () => {
		pollingRequests.clear();
		pollingFailureCount.clear();
		isPollingActive = false;
	};

	return {
		startPolling,
		stopPolling,
		resetPolling,
	};
};

// 删除相关
export const deleteModel = ref(false);

// 导出删除标志供Vue组件使用
export const isDeletingServer = ref(false);

export const confirmDelete = (close) => {
	close();
	// 设置删除标志，防止虚拟列表数据竞争
	isDeletingServer.value = true;

	onActionDelete(activeServer.value);

	setTimeout(() => {
		hideContextMenu();
		// 删除操作完成后重置标志
		setTimeout(() => {
			isDeletingServer.value = false;
		}, 500);
	}, 200);
	pageContainer.value.notify.success($t('common.deleteSuccess'));
};

export const onActionDelete = (item) => {
	const { serverList } = useServerListStore().getReactiveState();
	const { configList } = useConfigStore().getReactiveState();

	// 【修复】增强删除操作的原子性，确保所有相关数据同步更新
	try {
		// 增强配置匹配逻辑 - 使用多种方式查找配置项
		let configIndex = -1;

		// 方式1: 直接路径匹配
		configIndex = configList.value.findIndex((configItem) => configItem.panelPath === item.rawPath);

		// 方式2: 如果直接匹配失败，尝试通过SECRET值（panelKey）匹配
		if (configIndex === -1) {
			configIndex = configList.value.findIndex((configItem) => {
				// SECRET值有效性验证，如果panelKey不存在或为空，回退到panelPath
				const configSecretKey =
					configItem.panelKey && configItem.panelKey.trim() !== ''
						? configItem.panelKey
						: configItem.panelPath;
				return item.uniqueKey === configSecretKey;
			});
		}

		// 方式3: 如果还是失败，尝试标准化路径匹配
		if (configIndex === -1) {
			const itemNormalizedPath = normalizeURL(item.rawPath);
			configIndex = configList.value.findIndex((configItem) => {
				const configNormalizedPath = normalizeURL(configItem.panelPath);
				return itemNormalizedPath === configNormalizedPath;
			});
		}

		// 【修复】使用更严格的服务器匹配逻辑，基于SECRET值进行匹配
		const serverIndicesToRemove = [];
		serverList.value.forEach((serverItem, index) => {
			const isMatch = serverItem.uniqueKey === item.uniqueKey || serverItem.rawPath === item.rawPath;

			if (isMatch) {
				serverIndicesToRemove.push(index);
			}
		});

		// 【修复】从后往前删除服务器项，避免索引偏移
		for (let i = serverIndicesToRemove.length - 1; i >= 0; i--) {
			const index = serverIndicesToRemove[i];
			serverList.value.splice(index, 1);
		}

		// 从配置列表中删除
		if (configIndex !== -1) {
			configList.value.splice(configIndex, 1);
			uni.setStorageSync('configList', configList.value);
		}

		// 【修复】彻底清理排序列表中的已删除服务器
		const savedSortOrder = uni.getStorageSync('serverSortOrder');
		if (savedSortOrder && Array.isArray(savedSortOrder) && savedSortOrder.length > 0) {
			// 使用多种匹配方式确保彻底移除
			const updatedSortOrder = savedSortOrder.filter((rawPath) => {
				// 直接路径匹配
				if (rawPath === item.rawPath) return false;

				// 标准化路径匹配（处理可能的URL格式差异）
				const normalizedSavedPath = normalizeURL(rawPath);
				const normalizedItemPath = normalizeURL(item.rawPath);
				if (normalizedSavedPath === normalizedItemPath) return false;

				return true;
			});

			// 强制更新排序列表，确保删除操作立即生效
			uni.setStorageSync('serverSortOrder', updatedSortOrder);

			// 【新增】设置一个标记，表示排序已更改
			uni.setStorageSync('sortOrderChanged', true);
		}

		// 【关键修复】立即同步更新虚拟列表数据
		syncVirtualListAfterDelete(item);

		// 【新增】立即重新应用排序，确保删除后的列表顺序正确
		applyFinalSorting();

		// 【关键修复】强制刷新z-paging组件，确保删除操作立即生效
		if (paging.value) {
			// 使用nextTick确保DOM更新完成后再刷新
			nextTick(() => {
				// 使用setLocalPaging重新设置数据，确保虚拟列表正确更新
				paging.value
					.setLocalPaging(serverList.value)
					.then(() => {
						// 强制触发虚拟列表重新渲染
						if (paging.value.updateVirtualListRender) {
							paging.value.updateVirtualListRender();
						}
					})
					.catch(() => {
						// 删除后更新列表失败的静默处理
					});
			});
		}
	} catch (error) {
		// 如果删除操作失败，尝试恢复数据一致性
		setTimeout(() => {
			if (paging.value) {
				paging.value.reload();
			}
		}, 500);
	}
};

export const navigateServerInfo = (server, callback) => {
	throttle(() => {
		const { status, name } = server;
		if (!status || name === $t('server.connecting')) {
			return pageContainer.value.notify.error($t('server.serverConnectionFailed'));
		}
		handleServerInfo(server);
		if (SYSTEM.value !== 'linux') {
			return pageContainer.value.notify.error('windows系统暂未开放');
		}
		callback && callback();
	});
};

export const handleServerInfo = (server) => {
	const { rawPath } = server;
	const { configList } = useConfigStore().getReactiveState();

	// 增强配置匹配逻辑 - 使用多种方式查找配置项，特别处理IPv6地址
	let item = configList.value.find((configItem) => configItem.panelPath === rawPath);

	// 方式2: 如果直接匹配失败，尝试通过SECRET值（panelKey）匹配
	if (!item) {
		item = configList.value.find((configItem) => {
			// SECRET值有效性验证，如果panelKey不存在或为空，回退到panelPath
			const configSecretKey =
				configItem.panelKey && configItem.panelKey.trim() !== '' ? configItem.panelKey : configItem.panelPath;
			return server.uniqueKey === configSecretKey;
		});
	}

	// 方式3: 如果还是失败，尝试标准化路径匹配
	if (!item) {
		const normalizedRawPath = normalizeURL(rawPath);
		item = configList.value.find((configItem) => {
			const normalizedPanelPath = normalizeURL(configItem.panelPath);
			return normalizedRawPath === normalizedPanelPath;
		});
	}

	// 如果仍然找不到配置项，直接返回，避免解构undefined
	if (!item) {
		return;
	}

	const { panelPath, panelKey, aesKey, uuid, system } = item;
	PATH.value = panelPath;
	UUID.value = uuid;
	SECRET.value = panelKey;
	AESKEY.value = aesKey;
	SYSTEM.value = system;
	getCurrentServer(server);
};

// 获取当前选中的服务器
export const getCurrentServer = (server) => {
	const { currentServerInfo } = useConfigStore().getReactiveState();
	currentServerInfo.value = server;
	return server;
};

export const navigateServer = (server) => {
	// 【新增】检查服务器是否处于加载状态或手动添加状态
	if (server.isLoadingStatus || server.isManuallyAdding) {
		pageContainer.value.notify.warning('服务器正在连接中，请稍候...');
		return;
	}

	navigateServerInfo(server, () => {
		uni.navigateTo({
			url: `/linux/index/index`,
			animationType: 'zoom-fade-out',
		});
	});
};

// 长按菜单相关
export const showContextMenu = ref(false);
export const activeServer = ref(null);
export const activeIndex = ref(-1);
export const menuPosition = ref({
	top: '50%',
	left: '50%',
	class: '',
});
export const clonePosition = ref({
	top: '0px',
	left: '0px',
	width: '0px',
	height: '0px',
});

// 触摸状态管理
export const touchStartTime = ref(0);
export const touchStartPos = ref({ x: 0, y: 0 });
export const isTouchMoved = ref(false);
export const longPressTimer = ref(null);
export const LONG_PRESS_THRESHOLD = 600; // 长按阈值，单位毫秒
export const MOVE_THRESHOLD = 10; // 移动阈值，单位像素

// 菜单高度管理
export const actualMenuHeight = ref(80); // 预估高度，实际会测量
export const showTempMenu = ref(false);

// 测量菜单高度的方法
export const measureMenuHeight = () => {
	// 显示临时测量菜单
	showTempMenu.value = true;

	// 等待临时菜单渲染完成
	nextTick(() => {
		uni.createSelectorQuery()
			.select('.temp-measure-menu')
			.boundingClientRect((rect) => {
				if (rect && rect.height > 0) {
					actualMenuHeight.value = rect.height;
				}
				// 隐藏临时菜单
				showTempMenu.value = false;
			})
			.exec();
	});
};

// 触摸处理相关函数
export const handleTouchStart = (event) => {
	// 清除可能存在的定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
	}

	// 记录触摸开始时间和位置
	touchStartTime.value = Date.now();
	touchStartPos.value = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};
	isTouchMoved.value = false;

	// 设置长按定时器
	longPressTimer.value = setTimeout(() => {
		if (!isTouchMoved.value) {
			const index = event.currentTarget.dataset.index;
			const serverData = JSON.parse(event.currentTarget.dataset.server);

			// 【新增】检查服务器是否处于加载状态，禁用长按菜单
			if (serverData.isLoadingStatus || serverData.isManuallyAdding) {
				pageContainer.value.notify.warning('服务器正在连接中，无法执行操作');
				return;
			}

			showFloatingMenu(serverData, event, index);
		}
	}, LONG_PRESS_THRESHOLD);
};

export const handleTouchMove = (event) => {
	if (!touchStartPos.value) return;

	// 计算移动距离
	const moveX = Math.abs(event.touches[0].clientX - touchStartPos.value.x);
	const moveY = Math.abs(event.touches[0].clientY - touchStartPos.value.y);

	// 如果移动超过阈值，标记为已移动并取消长按定时器
	if (moveX > MOVE_THRESHOLD || moveY > MOVE_THRESHOLD) {
		isTouchMoved.value = true;

		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
	}
};

export const handleTouchEnd = (event) => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}

	// 如果未移动且是短触摸（非长按），则打开文件
	if (!isTouchMoved.value && Date.now() - touchStartTime.value < LONG_PRESS_THRESHOLD) {
		const serverData = JSON.parse(event.currentTarget.dataset.server);

		// 【新增】检查服务器是否处于加载状态，禁用点击导航
		if (serverData.isLoadingStatus || serverData.isManuallyAdding) {
			pageContainer.value.notify.warning('服务器正在连接中，请稍候...');
			return;
		}

		navigateServer(serverData);
	}
};

export const handleTouchCancel = () => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}
};

// 显示悬浮菜单 - 两阶段定位
export const showFloatingMenu = (server, _event, index) => {
	// 触感反馈
	triggerVibrate();

	activeServer.value = server;
	activeIndex.value = index;

	// 获取系统信息，用于检测是否会超出屏幕
	const systemInfo = uni.getSystemInfoSync();
	const screenHeight = systemInfo.windowHeight;
	const screenWidth = systemInfo.windowWidth;

	// 获取被长按元素相对于页面的位置
	uni.createSelectorQuery()
		.selectAll('.server-item')
		.boundingClientRect((rects) => {
			if (!rects || !rects[index]) return;

			const rect = rects[index];

			// 设置克隆项位置
			clonePosition.value = {
				top: `${rect.top}px`,
				left: `${rect.left}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
			};

			// 预估参数
			const tabbarHeight = 60; // 底部导航栏高度
			const headerHeight = 50; // 顶部标题栏高度
			const menuWidth = 200; // 菜单宽度（rpx，与CSS中的width保持一致）
			const menuWidthPx = menuWidth / 2; // 转换为px（uni-app中rpx到px的转换比例约为2:1）
			const edgeBuffer = 10; // 边缘安全距离

			// 计算菜单位置
			let menuTop,
				menuLeft,
				menuClass = '';

			// 水平定位 - 居中显示，使用transform: translateX(-50%)实现真正居中
			// menuLeft应该是菜单中心点的位置，因为CSS使用了translateX(-50%)
			menuLeft = rect.left + rect.width / 2;

			// 防止菜单超出屏幕左侧（考虑transform偏移）
			if (menuLeft - menuWidthPx / 2 < edgeBuffer) {
				menuLeft = menuWidthPx / 2 + edgeBuffer;
			}
			// 防止菜单超出屏幕右侧（考虑transform偏移）
			if (menuLeft + menuWidthPx / 2 > screenWidth - edgeBuffer) {
				menuLeft = screenWidth - menuWidthPx / 2 - edgeBuffer;
			}

			// 垂直定位 - 智能判断上方还是下方
			// 计算下方可用空间和上方可用空间
			const spaceBelow = screenHeight - rect.bottom - tabbarHeight;
			const spaceAbove = rect.top - headerHeight;

			// 优先考虑下方显示，如果下方空间不足，再考虑上方
			if (spaceBelow >= actualMenuHeight.value + edgeBuffer) {
				// 下方有足够空间
				menuTop = rect.bottom + edgeBuffer;
				menuClass = 'menu-bottom';
			} else if (spaceAbove >= actualMenuHeight.value + edgeBuffer) {
				// 上方有足够空间 - 菜单底部紧贴克隆项顶部
				menuTop = rect.top - actualMenuHeight.value - edgeBuffer;
				menuClass = 'menu-top menu-position-bottom'; // 添加菜单位置标记
			} else {
				// 两边都没有理想空间，选择空间较大的一边
				if (spaceBelow >= spaceAbove) {
					// 使用下方剩余空间
					menuTop = rect.bottom + edgeBuffer;
					menuClass = 'menu-bottom';
				} else {
					// 使用上方剩余空间 - 菜单底部紧贴克隆项顶部
					menuTop = rect.top - actualMenuHeight.value;
					menuClass = 'menu-top menu-position-bottom';
				}
			}

			// 设置菜单初始位置和样式
			menuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
				class: menuClass,
			};

			// 显示菜单
			showContextMenu.value = true;

			// 第二阶段：在菜单渲染后微调位置
			nextTick(() => {
				// 获取实际菜单高度
				uni.createSelectorQuery()
					.select('.context-menu')
					.boundingClientRect((menuRect) => {
						if (!menuRect) return;

						const actualMenuHeight = menuRect.height;

						// 如果菜单显示在上方，需要向上偏移菜单高度
						if (menuClass.includes('menu-position-bottom')) {
							// 确保菜单底部与克隆项顶部对齐
							const adjustedTop = rect.top - actualMenuHeight;
							menuPosition.value.top = `${adjustedTop}px`;
						}

						// 如果实际菜单宽度与预估不同，调整水平居中
						const actualMenuWidth = menuRect.width;
						const expectedWidthPx = menuWidth / 2; // 预期宽度转换为px

						if (Math.abs(actualMenuWidth - expectedWidthPx) > 5) {
							// 重新计算水平位置（菜单中心点位置，因为CSS使用了translateX(-50%)）
							let adjustedLeft = rect.left + rect.width / 2;

							// 防止菜单超出屏幕左侧（考虑transform偏移）
							if (adjustedLeft - actualMenuWidth / 2 < edgeBuffer) {
								adjustedLeft = actualMenuWidth / 2 + edgeBuffer;
							}
							// 防止菜单超出屏幕右侧（考虑transform偏移）
							if (adjustedLeft + actualMenuWidth / 2 > screenWidth - edgeBuffer) {
								adjustedLeft = screenWidth - actualMenuWidth / 2 - edgeBuffer;
							}

							menuPosition.value.left = `${adjustedLeft}px`;
						}
					})
					.exec();
			});
		})
		.exec();
};

// 隐藏悬浮菜单
export const hideContextMenu = () => {
	showContextMenu.value = false;
	activeServer.value = null;
	activeIndex.value = -1;
};

// 确认删除服务器
export const confirmDeleteServer = () => {
	// 使用现有的删除逻辑
	onActionDelete(activeServer.value);
	// 隐藏菜单
	hideContextMenu();
};

export const onActionClick = (e, item) => {
	// 【新增】检查服务器是否处于加载状态，禁用操作
	if (item.isLoadingStatus || item.isManuallyAdding) {
		pageContainer.value.notify.warning('服务器正在连接中，无法执行操作');
		return;
	}

	// e.index 对应着滑动列表的索引
	if (e.index === 0) {
		activeServer.value = item;
		deleteModel.value = true;
	}
};

// 更多菜单相关状态管理
export const showMoreMenu = ref(false);
export const moreMenuPosition = ref({
	top: '100px',
	left: '50%',
	transform: 'translateX(-50%)', // 水平居中
});

// 切换更多菜单显示状态
export const toggleMoreMenu = () => {
	if (showMoreMenu.value) {
		hideMoreMenu();
	} else {
		showMoreMenuDropdown();
	}
};

// 显示更多菜单下拉框
export const showMoreMenuDropdown = () => {
	// 获取导航栏右侧按钮的位置
	uni.createSelectorQuery()
		.select('.more-server-icon')
		.boundingClientRect((rect) => {
			if (!rect) {
				// 如果无法获取按钮位置，使用默认定位
				moreMenuPosition.value = {
					top: '100px',
					right: '20px',
				};
				showMoreMenu.value = true;
				return;
			}

			// 计算菜单位置 - 居中对齐，位于按钮下方
			const menuTop = rect.bottom + 8; // 位于按钮下方8rpx
			const menuWidth = 220; // 菜单宽度（rpx转px，约为240/2=120px）
			const buttonCenterX = rect.left + rect.width / 2; // 按钮中心X坐标

			// 获取屏幕宽度
			const systemInfo = uni.getSystemInfoSync();
			const screenWidth = systemInfo.windowWidth;

			// 计算菜单左边距，使菜单中心与按钮中心对齐
			let menuLeft = buttonCenterX - menuWidth / 4; // 除以4是因为rpx到px的转换

			// 确保菜单不会超出屏幕右边界
			const menuRight = screenWidth - menuLeft - menuWidth / 2;
			if (menuRight < 20) {
				menuLeft = screenWidth - menuWidth / 2 - 20;
			}

			// 确保菜单不会超出屏幕左边界
			if (menuLeft < 20) {
				menuLeft = 20;
			}

			moreMenuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
			};

			showMoreMenu.value = true;
		})
		.exec();
};

// 隐藏更多菜单
export const hideMoreMenu = () => {
	showMoreMenu.value = false;
};

// 处理扫一扫点击
export const handleScanClick = (startScanCallback) => {
	hideMoreMenu();
	// 调用传入的扫描函数
	if (startScanCallback && typeof startScanCallback === 'function') {
		startScanCallback();
	}
};

// IP隐藏状态管理
export const isIPHidden = ref(false);

// 初始化IP隐藏状态
export const initIPHiddenState = () => {
	// 从本地存储读取IP隐藏状态
	const savedState = uni.getStorageSync('isIPHidden');
	isIPHidden.value = savedState === true || savedState === 'true';
};

// 格式化IP显示（支持IPv4和IPv6）
export const formatIPDisplay = (ip) => {
	if (!isIPHidden.value) {
		return ip;
	}

	// 使用工具函数进行IPv4和IPv6兼容的格式化
	return formatIPDisplayUtil(ip, true);
};

// 处理隐藏IP点击
export const handleHideIPClick = () => {
	hideMoreMenu();

	// 切换IP隐藏状态
	isIPHidden.value = !isIPHidden.value;

	// 保存到本地存储
	uni.setStorageSync('isIPHidden', isIPHidden.value);

	// 显示提示信息
	const message = isIPHidden.value ? '已隐藏IP地址' : '已显示IP地址';
	pageContainer.value.notify.success(message);
};

// 处理排序点击
export const handleSortClick = () => {
	hideMoreMenu();

	// 跳转到排序页面
	uni.navigateTo({
		url: '/pages/index/serverList/sort/index',
		animationType: 'slide-in-right',
	});
};

// 导出generateSafeCanvasId函数供其他组件使用
export { generateSafeCanvasId };

// 【新增】根据负载使用率获取状态文本
export const getLoadStatusText = (usage) => {
	const usageNum = typeof usage === 'number' ? usage : parseFloat(usage) || 0;

	if (usageNum >= 90) {
		return '严重';
	} else if (usageNum >= 81) {
		return '繁忙';
	} else if (usageNum >= 61) {
		return '较高';
	} else if (usageNum >= 41) {
		return '正常';
	} else if (usageNum >= 21) {
		return '良好';
	} else {
		return '流畅';
	}
};

// 【新增】根据磁盘使用率获取状态文本
export const getDiskStatusText = (usage) => {
	const usageNum = typeof usage === 'number' ? usage : parseFloat(usage) || 0;

	if (usageNum >= 95) {
		return '满载';
	} else if (usageNum >= 90) {
		return '严重';
	} else if (usageNum >= 81) {
		return '紧张';
	} else if (usageNum >= 61) {
		return '较高';
	} else if (usageNum >= 41) {
		return '正常';
	} else if (usageNum >= 21) {
		return '良好';
	} else {
		return '充足';
	}
};

// 通用图表样式配置函数
const getChartStyleConfig = (usage, status, chartType, getStatusText, serverStatus) => {
	// 数据安全性检查和默认值处理
	if (typeof usage !== 'number') {
		usage = parseFloat(usage) || 0;
	}

	// 根据使用率计算尺寸（5% - 95%范围，确保可见性）
	const dimension = Math.max(5, Math.min(95, usage)) + '%';

	// 使用统一的渐变配置，与 getChartColorStops 保持一致
	let background, statusClass, color;

	if (usage >= 81) {
		// 81-100% 从红色到黄色的渐变
		if (chartType === 'load') {
			background = 'linear-gradient(180deg,  #FFEB3B 0%, #D32F2F 100%)';
		} else {
			background = 'linear-gradient(90deg, #D32F2F 0%, #FFEB3B 100%)';
		}
		statusClass = chartType === 'load' ? 'load-status-high' : 'disk-status-high';
		color = '#D32F2F';
	} else if (usage >= 51) {
		// 51-80% 从绿色到黄色的渐变
		if (chartType === 'load') {
			background = 'linear-gradient(180deg, #FFEB3B 0%, #4CAF50 100%)';
		} else {
			background = 'linear-gradient(90deg, #4CAF50 0%, #FFEB3B 100%)';
		}
		statusClass = chartType === 'load' ? 'load-status-medium' : 'disk-status-medium';
		color = '#4CAF50';
	} else {
		// 0-50% 纯绿色
		if (chartType === 'load') {
			background = 'linear-gradient(180deg, #4CAF50 0%, #4CAF50 100%)';
		} else {
			background = 'linear-gradient(90deg, #4CAF50 0%, #4CAF50 100%)';
		}
		statusClass = chartType === 'load' ? 'load-status-normal' : 'disk-status-normal';
		color = '#4CAF50';
	}

	// 根据图表类型返回相应的尺寸属性名
	const dimensionProp = chartType === 'load' ? { height: dimension } : { width: dimension };

	if (!serverStatus) {
		background = '#E7E7E7';
	}

	return {
		usage: usage.toFixed(0), // 显示整数百分比
		...dimensionProp,
		background,
		status: status || getStatusText(usage),
		statusClass,
		...(chartType === 'disk' && { color }), // 磁盘图表特有的颜色属性
	};
};

// 重构后的负载图表样式配置函数
export const getLoadChartStyle = (loadData, serverStatus = true) => {
	// 数据安全性检查和默认值处理
	if (!loadData || typeof loadData !== 'object') {
		return {
			usage: 0,
			height: '5%',
			background: '#e0e0e0',
			status: '未知',
			statusClass: 'load-status-normal',
		};
	}

	const usage = typeof loadData.usage === 'number' ? loadData.usage : parseFloat(loadData.usage) || 0;
	const status = loadData.total;

	// 使用通用配置函数
	return getChartStyleConfig(usage, status, 'load', getLoadStatusText, serverStatus);
};

// 重构后的磁盘图表样式配置函数
export const getDiskChartStyle = (diskData, serverStatus = true) => {
	// 数据安全性检查和默认值处理
	if (!diskData || typeof diskData !== 'object') {
		return {
			usage: 0,
			width: '5%',
			background: '#e0e0e0',
			status: '未知',
			statusClass: 'disk-status-normal',
			color: '#A7A7A7', // 默认颜色
		};
	}

	const usage = typeof diskData.usage === 'number' ? diskData.usage : parseFloat(diskData.usage) || 0;
	const status = diskData.total;

	// 使用通用配置函数
	return getChartStyleConfig(usage, status, 'disk', getDiskStatusText, serverStatus);
};
